{"name": "crimson-websites-next", "version": "0.2.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch", "proxy": "npx local-ssl-proxy --source 3010 --target 3000 --cert localhost.pem --key localhost-key.pem", "analyze": "ANALYZE=true next build"}, "dependencies": {"@crimson-education/algorithms-school-selection-common": "^0.1.16", "@crimson-education/helios-editor-renderer": "0.5.0", "@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@next/third-parties": "^15.0.3", "@storyblok/react": "^3.0.15", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dayjs-abbr-timezone": "^1.0.0", "eslint-plugin-tailwindcss": "^3.17.5", "flag-icons": "^7.5.0", "formik": "^2.4.6", "geist": "^1.3.0", "html-react-parser": "^5.2.2", "jotai": "^2.10.1", "js-cookie": "^3.0.5", "motion": "^12.9.2", "next": "^15.4.1", "nuka-carousel": "^8.1.1", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-papaparse": "^4.4.0", "react-select": "^5.8.2", "react-share": "^5.2.2", "react-windowed-select": "^5.2.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "superjson": "^2.2.1", "tailwind-merge": "^2.5.4", "tailwind-scrollbar": "^4.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "zod": "^3.23.3"}, "devDependencies": {"@next/bundle-analyzer": "^15.0.3", "@playwright/test": "^1.48.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/eslint": "^8.56.10", "@types/jest": "^29.5.13", "@types/js-cookie": "^3.0.6", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^14.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "pnpm@9.7.0"}