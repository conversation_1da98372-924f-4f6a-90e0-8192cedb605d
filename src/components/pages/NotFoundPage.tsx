"use client";

import Image from "next/image";
import Link from "next/link";
import SlideCarousel from "@/components/ui/SlideCarousel";
import VectorLogo from "@/images/404/Vector.svg";
import Text from "@/components/ui/Text";
import Card1Image from "@/images/404/404-1.webp";
import Card2Image from "@/images/404/404-2.webp";
import Card3Image from "@/images/404/404-3.webp";
import ServiceCard from "@/components/ui/ServiceCard";
import Container from "@/components/ui/Container";
import { cn } from "@/common/utils";

const getServiceCards = (locale: string) => [
  {
    id: 1,
    title: "US Admissions Consulting",
    image: Card1Image,
    link: `/${locale}/services/us-universities/`,
    cta: "Learn More →",
  },
  {
    id: 2,
    title: "Our Results",
    image: Card2Image,
    link: `/${locale}/about-us/student-results/`,
    cta: "Explore Our Results →",
  },
  {
    id: 3,
    title: "Resources Library",
    image: Card3Image,
    link: `/${locale}/blog/`,
    cta: "Explore Our Resources →",
  },
];

interface NotFoundPageProps {
  locale?: string;
}

const NotFoundPage = ({ locale = 'us' }: NotFoundPageProps) => {
  const serviceCards = getServiceCards(locale);

  const cardsPerViewGetter = (width: number) => {
    if (width <= 375) {
      return 1;
    } else {
      return 2;
    }
  };

  return (
    <div className="bg-grays-G6 min-h-screen">

      <section className="relative pt-20">
        <Container className="!py-0 !pt-10">
          <div className="flex w-full gap-6 flex-col items-center justify-center sm:flex-row sm:items-center sm:justify-between sm:gap-8 lg:gap-12 2xl:gap-[11.25rem] 2xl:items-center">
            <div className="flex flex-col items-start justify-start gap-4 w-full sm:flex-1 sm:max-w-none sm:pr-16 lg:pr-24 2xl:flex-none 2xl:w-[39.75rem] 2xl:max-w-[39.75rem] 2xl:pr-0 2xl:justify-center">
              <Text
                tag="h1"
                style="sh3"
                mdStyle="sh2"
                className="text-left italic text-primary01-50"
              >
                Page not found.
              </Text>

              <Text
                tag="p"
                style="b2"
                mdStyle="b1"
                className="text-left text-neutral01-75"
              >
                You've landed on a page that no longer exists — but your path to top universities doesn't have to go off-track.
              </Text>

              <Link
                href={`/${locale}`}
                className="transition-colors hover:text-primary01-50"
              >
                <Text
                  tag="span"
                  style="b2"
                  className="font-display-sans font-semibold underline leading-[120%] text-primary01-75"
                >
                  Take me home.
                </Text>
              </Link>
            </div>

            <div className="hidden sm:flex items-center justify-center flex-shrink-0 sm:w-auto 2xl:w-[28.9375rem] 2xl:items-center 2xl:justify-center">
              <Image
                src={VectorLogo}
                alt="Crimson Education Logo"
                width={343}
                height={380}
                className="w-48 h-52 sm:w-60 sm:h-64 md:w-72 md:h-80 lg:w-80 lg:h-96 xl:w-96 xl:h-[26rem] 2xl:w-[28.9375rem] 2xl:h-[32rem]"
                priority
                unoptimized
              />
            </div>
          </div>
        </Container>
      </section>

      <section className="relative mt-20 pb-32">
        <Container className="!py-0">
          <Text
            tag="h2"
            style="mh1.5"
            mdStyle="h2"
            className="mb-5 md:mb-20 text-left text-primary01-75"
          >
            Let's get you back on course
          </Text>

          <div className="block md:hidden">
            <SlideCarousel
              transparent
              wrapperClassName="!bg-transparent !pt-0"
              innerWrapperClassName="!px-0"
              cardsPerViewGetter={cardsPerViewGetter}
              autoCardWrapper={false}
            >
              {serviceCards.map((card, index) => (
                <div
                  key={card.id}
                  className={cn(
                    "flex-none w-full sm:w-[calc((100%-16px)/2)]",
                    index < serviceCards.length - 1 && "mr-4"
                  )}
                >
                  <ServiceCard card={card} />
                </div>
              ))}
            </SlideCarousel>
          </div>

          <div className="hidden md:flex md:items-start md:justify-between md:w-full md:gap-6">
            {serviceCards.map((card) => (
              <div
                key={card.id}
                className="flex-1"
              >
                <ServiceCard card={card} />
              </div>
            ))}
          </div>
        </Container>
      </section>
    </div>
  );
};

export default NotFoundPage;