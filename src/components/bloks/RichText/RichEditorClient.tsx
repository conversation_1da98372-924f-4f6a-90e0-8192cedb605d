/**
 * crimson rich text editor client component
 * This component is used to render rich text content in a client-side environment.
 * do not use @crimson-education/helios-editor-renderer directly in other files !
 */
"use client";

import { RichEditor } from "@crimson-education/helios-editor-renderer";
import { SectionItem } from "@/common/types";
import ErrorBoundary from "./ErrorBoundary";

interface RichTextProps {
    value: SectionItem["content"];
}

const RichEditorClient: React.FC<RichTextProps> = ({ value }) => {
    if (!value || !Array.isArray(value)) return null;

    return (
        <div className="prose-helios prose">
            <ErrorBoundary>
                <RichEditor value={value} />
            </ErrorBoundary>
        </div>
    );
};

export default RichEditorClient;