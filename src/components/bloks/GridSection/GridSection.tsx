"use client";

import { cn } from "@/common/utils";
import Container from "@/components/ui/Container";
import { cva } from "class-variance-authority";
import React, { useState } from "react";
import GridItem from "./GridItem";
import Header from "./Header";
import Button from "@/components/ui/Button";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
import { IGridSectionComponentProps } from "./types";

const STEP_COUNT = 6;

export default function GridSection({ blok }: IGridSectionComponentProps) {
  const { t } = useLabelTranslation();
  const [sliceEnd, setSliceEnd] = useState(STEP_COUNT);
  const {
    gridItems,
    displayGridNumericIndex,
    gridItemLinkLabel,
    globalCollegeOffers,
  } = blok;

  const columnStyle = calculateGridColumns(gridItems?.length || 2);
  const title = blok.title[0];
  const isImageGrid =
    !displayGridNumericIndex &&
    gridItems?.some((item) => !!item.image?.filename);

  const loadMore = () =>
    setSliceEnd((slice) => Math.min(gridItems.length, slice + STEP_COUNT));

  return (
    <Container
      className={cn(
        title?.component === "componentTitleH4" && "pt-10 md:py-10",
      )}
    >
      {title && <Header title={title} />}

      <div className={cn("", variants({ cols: columnStyle, isImageGrid }))}>
        {gridItems.slice(0, sliceEnd).map((item, i) => (
          <GridItem
            key={i}
            displayGridNumericIndex={displayGridNumericIndex}
            {...item}
            index={i + 1}
            isImageGrid={isImageGrid}
            columnStyle={columnStyle}
            gridItemLinkLabel={gridItemLinkLabel}
            globalCollegeOffers={globalCollegeOffers}
          />
        ))}
      </div>
      {sliceEnd < gridItems.length && (
        <div className="flex justify-center">
          <Button
            theme="secondary"
            colour="maroon"
            className="my-auto mt-[4.56rem]"
            onClick={loadMore}
          >
            {t("Show More")}
          </Button>
        </div>
      )}
    </Container>
  );
}

const calculateGridColumns = (totalItems: number) => {
  switch (totalItems) {
    case 2:
      return 2;

    case 4:
    case 7:
    case 8:
      return 4;

    default:
      return 3;
  }
};

const variants = cva("grid grid-cols-1 md:grid-cols-2 gap-[73px]", {
  variants: {
    cols: {
      1: "flex flex-col gap-12",
      2: "md:grid-cols-2 gap-[73px]",
      3: "xl:grid-cols-3 xl:gap-[73px]",
      4: "xl:grid-cols-4 xl:gap-[73px]",
    },
    isImageGrid: {
      true: "lg:grid-cols-2",
    },
  },
  defaultVariants: {
    cols: 1,
  },
  compoundVariants: [
    {
      isImageGrid: true,
      cols: 4,
      className: "2xl:grid-cols-4 2xl:gap-[73px]",
    },
  ],
});
