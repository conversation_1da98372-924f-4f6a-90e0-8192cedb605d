import {
  ISbDataSourceResult,
  IStoryblokAssetProps,
  IStoryblokLinkProps,
} from "@/common/types";

export interface IGridItem {
  spotlightText: string;
  image: IStoryblokAssetProps;
  collegeOffers: string;
  heading: string;
  bodyContent: string;
  learnMoreLink: IStoryblokLinkProps[];
  globalCollegeOffers: ISbDataSourceResult["data"]["datasource_entries"];
}
export interface IGridSectionComponentProps {
  blok: {
    title: Title[];
    gridItems: IGridItem[];
    displayGridNumericIndex: boolean;
    gridItemLinkLabel: string;
    globalCollegeOffers: ISbDataSourceResult["data"]["datasource_entries"];
  };
}
export interface Title {
  preHeading?: string;
  heading: string;
  bodyContent?: string;
  component: string;
}
