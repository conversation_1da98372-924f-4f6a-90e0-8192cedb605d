import React from "react";
import { IGridSectionComponentProps } from "./types";
import GridSection from "./GridSection";
import { fetchDataSource } from "@/common/storyblok";
import { ISbDataSourceResult } from "@/common/types";

export default async function GridSectionServer(
  props: IGridSectionComponentProps,
) {
  const { gridItems } = props.blok;

  const data: ISbDataSourceResult = await fetchDataSource(
    "stats-items-2",
    false,
    "result",
  );
  const globalCollegeOffers = data?.data?.datasource_entries;

  const transformedGridItems = gridItems.map((gridItem) => {
    const learnMoreLink = gridItem.learnMoreLink;

    if (!learnMoreLink || learnMoreLink.length === 0) {
      gridItem.learnMoreLink = [];
      return gridItem;
    }
    return gridItem;
  });

  return (
    <GridSection
      blok={{
        ...props.blok,
        gridItems: transformedGridItems,
        globalCollegeOffers,
      }}
    />
  );
}
