import React from "react";
import { IGridItem } from "./types";
import Text from "@/components/ui/Text";
import Image from "next/image";
import { cn, generateImageAltFromFilename } from "@/common/utils";
import { cva } from "class-variance-authority";
import Button from "@/components/ui/Button";
import GeneralLink from "../GeneralLink";

interface GridProps extends IGridItem {
  displayGridNumericIndex: boolean;
  index: number;
  isImageGrid?: boolean;
  columnStyle?: number;
  gridItemLinkLabel?: string;
}

export default function GridItem(props: GridProps) {
  const {
    displayGridNumericIndex,
    image,
    spotlightText,
    collegeOffers,
    index,
    heading,
    bodyContent,
    isImageGrid,
    columnStyle,
    gridItemLinkLabel,
    learnMoreLink,
    globalCollegeOffers,
  } = props;

  const showIndex = displayGridNumericIndex;
  const showImage = !!image?.filename && !showIndex;
  const shouldIncreaseHeight = isImageGrid && !image.filename;
  const imageHeightClassname = imageHeightVariants({
    style: columnStyle as any,
  });
  const collegeOfferValue =
    globalCollegeOffers?.find((offer) => offer.name === collegeOffers)
      ?.dimension_value ?? "";

  const showSpotlight =
    !displayGridNumericIndex && !showImage && (spotlightText || collegeOffers);

  const isLinkValid = learnMoreLink && learnMoreLink.length > 0;

  return (
    <div className={cn("flex flex-col", showIndex && "pl-6")}>
      <div
        className={cn(
          "mb-6 flex flex-col gap-4",
          showIndex && "-ml-6 border-l border-l-[#D9D9D9] pl-6",
        )}
      >
        <div
          className={cn(
            shouldIncreaseHeight ? imageHeightClassname : "",
            shouldIncreaseHeight && "flex items-end",
          )}
        >
          {showImage && (
            <Image
              src={image.filename}
              width={1024}
              height={768}
              // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
              alt={`${generateImageAltFromFilename(image.filename) || image.alt || heading || "Grid image"}`}
              className={cn(imageHeightClassname, "w-full object-cover")}
            />
          )}
          {showSpotlight && (
            <Text tag="p" style="h2" className="text-primary01-50">
              {spotlightText || collegeOfferValue}
            </Text>
          )}
          {showIndex && (
            <Text tag="p" style="sh2" className="text-primary01-50">
              {formatNumber(index)}
            </Text>
          )}
        </div>
        {heading && (
          <Text tag="h3" style="h5" className="min-h-6 text-primary01-100">
            {heading}
          </Text>
        )}
      </div>
      <Text tag="p" style="b2" mdStyle="b1" className="mb-6 text-neutral01-75">
        {bodyContent}
      </Text>
      {isLinkValid && learnMoreLink[0] && (
        <GeneralLink blok={learnMoreLink[0]}>
          <Button theme="link1" colour="maroon">
            {gridItemLinkLabel || "Learn More"}
            {" →"}
          </Button>
        </GeneralLink>
      )}
    </div>
  );
}

const formatNumber = (n: number) => {
  try {
    return n.toString().padStart(2, "0");
  } catch {
    return n;
  }
};

const imageHeightVariants = cva("h-[200px]", {
  variants: {
    style: {
      2: "md:h-[12.15231rem] lg:h-[17.07063rem] xl:h-[19.17575rem] 2xl:h-[23.27119rem]",
      3: "xl:h-[11.8525rem] 2xl:h-[14.5955rem]",
      4: "2xl:h-[10.24813rem]",
    },
  },
});
