import { storyblokEditable } from "@storyblok/react/rsc";
import Text from "@/components/ui/Text";

interface Props {
  blok: {
    heading: string;
    bodyContent?: string;
    component: "componentTitleH2Centered";
    _uid: string;
  };
}

const ComponentTitleH2Centered = ({ blok }: Props) => {
  const { heading, bodyContent } = blok;

  return (
    <div {...storyblokEditable(blok)} className="flex flex-col items-center gap-lg text-center">
      <Text
        tag="h2"
        style="h3"
        mdStyle="h2"
        className="text-primary01-75"
      >
        {heading}
      </Text>

      {bodyContent && (
        <Text
          tag="p"
          style="mb1"
          mdStyle="b1"
          className="text-neutral01-75"
        >
          {bodyContent}
        </Text>
      )}
    </div>
  );
};

export default ComponentTitleH2Centered;
