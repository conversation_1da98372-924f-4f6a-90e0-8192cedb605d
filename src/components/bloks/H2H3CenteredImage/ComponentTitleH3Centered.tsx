import { storyblokEditable } from "@storyblok/react/rsc";
import Text from "@/components/ui/Text";

interface Props {
  blok: {
    heading: string;
    bodyContent?: string;
    component: "componentTitleH3Centered";
    _uid: string;
  };
}

const ComponentTitleH3Centered = ({ blok }: Props) => {
  const { heading, bodyContent } = blok;

  return (
    <div {...storyblokEditable(blok)} className="flex flex-col items-center gap-lg text-center">
      <Text
        tag="h3"
        style="h4"
        mdStyle="h3"
        className="text-grays-G1"
      >
        {heading}
      </Text>

      {bodyContent && (
        <Text
          tag="p"
          style="mb1"
          mdStyle="b1"
          className="text-neutral01-75"
        >
          {bodyContent}
        </Text>
      )}
    </div>
  );
};

export default ComponentTitleH3Centered;
