import { storyblokEditable, StoryblokComponent } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import ImageWrapper from "@/components/ui/ImageWrapper";

interface HeadingItem {
  heading: string;
  component: "componentTitleH2Centered" | "componentTitleH3Centered";
  bodyContent?: string;
  _uid: string;
}

interface Props {
  blok: {
    image: {
      id: number;
      filename: string;
      fieldtype: "asset";
    };
    heading: HeadingItem[];
    component: "h2H3CenteredImage";
    _uid: string;
  };
}

const H2H3CenteredImage = ({ blok }: Props) => {
  const { image, heading } = blok;

  return (
    <div {...storyblokEditable(blok)}>
      <Container>
        <div className="flex flex-col items-center text-center">
          <div className="flex flex-col items-center gap-4xl w-full max-w-sm md:max-w-2xl lg:max-w-3xl">
            {heading?.map((item) => (
              <StoryblokComponent blok={item} key={item._uid} />
            ))}
          </div>

          {image?.filename && (
            <div className="mt-4xl w-full max-w-sm md:max-w-2xl lg:max-w-3xl">
              <ImageWrapper
                src={image.filename}
                alt=""
                width={645}
                height={512}
                className="h-auto w-full"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 90vw, (max-width: 1225px) 80vw, 645px"
              />
            </div>
          )}
        </div>
      </Container>
    </div>
  );
};

export default H2H3CenteredImage;
