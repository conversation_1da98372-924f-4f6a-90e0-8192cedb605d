import { cn } from "@/common/utils";
import React from "react";
import Container from "@/components/ui/Container";
import AnimatedIncrementor from "@/components/ui/AnimatedIncrementor";
import Table from "./Table";
import ChartComponent from "./Chart";
import { Grid<PERSON>ell, StyledParagraph, SwipeUpAnimation } from "./misc";
import StudentCarousel from "./StudentCarousel";
import { ISbDataSourceResult, IStudent } from "@/common/types";
import { College } from "./types";
import Link from "next/link";
import Text from "@/components/ui/Text";
import Button from "@/components/ui/Button";
import { storyblokEditable } from "@storyblok/react/rsc";
import { fetchDataSource } from "@/common/storyblok";

export interface IResultsProps {
  preHeading: string;
  heading: string;
  bodyContent: string;
  useCTA: boolean;
  buttonLabel?: string;
  link: string;
  language: string;
  collegeOffers: string;
  collegeOffersLabel: string;
  admissionsMultiplier: string;
  admissionsMultiplierLabel: string;
  students?: IStudent[];
  chartTitle: string;
  collegeAxisLabel: string;
  generalAcceptanceAxisLabel: string;
  crimsonAcceptanceAxisLabel: string;
  acceptanceRates: "us" | "mixed";
  topAdmissionsMetric: string;
  topAdmissionsLabel: string;
}

export default async function ResultStackSection(
  props: IResultsProps & { collegeAdmissionsResults: College[] },
) {
  const {
    students,
    useCTA,
    buttonLabel,
    link,
    preHeading,
    heading,
    bodyContent,
    collegeOffers,
    collegeOffersLabel,
    admissionsMultiplier,
    admissionsMultiplierLabel,
    acceptanceRates,
    chartTitle,
    collegeAxisLabel,
    generalAcceptanceAxisLabel,
    crimsonAcceptanceAxisLabel,
    topAdmissionsMetric,
    topAdmissionsLabel,
    collegeAdmissionsResults,
  } = props;

  const data: ISbDataSourceResult = await fetchDataSource(
    "stats-items-2",
    false,
    "result",
  );

  const globalCollegeOffers = data?.data?.datasource_entries;

  const collegeOfferValue =
    globalCollegeOffers?.find((offer) => offer.name === collegeOffers)
      ?.dimension_value ?? "";

  return (
    <section {...storyblokEditable({ ...props })}>
      <Container className="px-4 py-[30px] md:px-[30px] md:py-[50px] xl:p-[75px]">
        <section>
          <Text
            tag="h4"
            style="ph1"
            className="whitespace-pre-wrap italic text-primary01-50"
          >
            {preHeading}
          </Text>
          <Text
            tag="h2"
            style="mh1.5"
            mdStyle="h2"
            className="mb-2 whitespace-pre-wrap text-primary01-75"
          >
            {heading}
          </Text>
          <div
            className={cn(
              "mb-10 flex gap-8",
              "flex-col items-start",
              "lg:flex-row lg:items-center lg:justify-between",
            )}
          >
            <Text
              tag="p"
              style="b2"
              mdStyle="b1"
              className="whitespace-normal text-neutral01-75 sm:whitespace-nowrap"
            >
              {bodyContent}
            </Text>
            {useCTA && link && (
              <Link href={link}>
                <Button theme="secondary" colour="maroon">
                  {buttonLabel}
                </Button>
              </Link>
            )}
          </div>
          <div>
            <main
              id="rhs-stats"
              className="bg-blue row-auto grid grid-rows-[repeat(3,auto)] items-start gap-4 md:grid-cols-2 md:gap-x-[20px] md:gap-y-5 lg:grid-cols-[300px_230px_minmax(400px,auto)] xl:grid-cols-[315px_280px_minmax(435px,auto)] 2xl:grid-cols-[375px_375px_minmax(473px,auto)]"
            >
              <GridCell
                className={cn(
                  "h-full justify-center",
                  "lg:row-span-2 lg:items-center lg:gap-y-3",
                )}
              >
                <SwipeUpAnimation className="font-display-sans text-[6.25rem] leading-none xl:text-[8.125rem]">
                  {admissionsMultiplier}x
                </SwipeUpAnimation>
                <StyledParagraph>{admissionsMultiplierLabel}</StyledParagraph>
              </GridCell>
              <GridCell
                layout="cols"
                className={cn(
                  "grid size-full grid-cols-[max-content_auto] items-center gap-5",
                  "lg:flex lg:flex-col lg:items-start lg:justify-center lg:gap-2",
                )}
              >
                <Text
                  tag="h4"
                  style="h2"
                  className="relative font-bold leading-none text-primary01-50 lg:text-[3.125rem] xl:text-[4.375rem] [&_span:last-child]:absolute [&_span:last-child]:left-0 [&_span:last-child]:top-0 [&_span]:font-display-sans"
                >
                  {/*
                   * usecase: this allows to have consitent spacing irregardless
                   * of the number and mitigates layout shift with the animation :-]
                   */}
                  <span aria-hidden className="invisible">
                    {formatNumber(collegeOfferValue)}
                  </span>
                  <AnimatedIncrementor
                    target={`${collegeOfferValue}`}
                    startWhenInView
                    runOnce
                  />
                </Text>
                <StyledParagraph>{collegeOffersLabel}</StyledParagraph>
              </GridCell>
              <div
                className={cn(
                  "h-full max-h-full",
                  "md:row-span-3 md:row-start-1",
                  "md:col-start-2",
                  "lg:col-start-3",
                )}
              >
                <Table
                  title={chartTitle}
                  heading={[
                    collegeAxisLabel,
                    generalAcceptanceAxisLabel,
                    crimsonAcceptanceAxisLabel,
                  ]}
                  data={collegeAdmissionsResults ?? []}
                  className="lg:hidden"
                />
                <ChartComponent
                  title={chartTitle}
                  generalAcceptanceAxisLabel={generalAcceptanceAxisLabel}
                  crimsonAcceptanceAxisLabel={crimsonAcceptanceAxisLabel}
                  colleges={collegeAdmissionsResults ?? []}
                  resultsFor={acceptanceRates}
                  className="hidden lg:block"
                />
              </div>
              <GridCell
                className={cn(
                  "relative h-full overflow-hidden",
                  "lg:col-start-1 lg:row-span-3 lg:row-start-1 lg:bg-transparent lg:p-0",
                )}
              >
                <StudentCarousel
                  items={students ?? []}
                  className="hidden lg:block"
                />
                <div
                  className={cn(
                    "lg:absolute lg:z-[7] lg:text-white",
                    "flex h-full flex-col justify-center",
                    "lg:absolute lg:bottom-0 lg:h-auto lg:bg-gradient-to-t lg:from-neutral01-100 lg:from-20% lg:to-transparent lg:px-[1.63rem] lg:py-[1.62rem]",
                  )}
                >
                  <SwipeUpAnimation className="mb-2 text-h2 leading-none [&]:text-primary01-75 lg:[&]:text-white">
                    {topAdmissionsMetric}
                  </SwipeUpAnimation>
                  <StyledParagraph className="font-normal lg:text-white">
                    {topAdmissionsLabel}
                  </StyledParagraph>
                </div>
              </GridCell>
            </main>
          </div>
        </section>
      </Container>
    </section>
  );
}

const formatNumber = (input: string) => {
  try {
    const suffix = input.endsWith("+") ? "+" : "";
    const withoutPrefix = Number(suffix ? input.replaceAll(suffix, "") : input);

    return `${withoutPrefix.toLocaleString()}${suffix}`;
  } catch {
    return "";
  }
};
