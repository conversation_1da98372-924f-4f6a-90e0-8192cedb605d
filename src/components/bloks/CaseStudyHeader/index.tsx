import Image from "next/image";
import Text from "@/components/ui/Text";
import ShareButton from "@/components/ui/ShareButton";

import { hexToRgba, cn, generateImageAltFromFilename } from "@/common/utils";

import AcceptToAtTablet from "./AcceptToAtTablet";
import StudentInfoAtDesktop from "./StudentInfoAtDesktop";
import StudentInfoAtMobile from "./StudentInfoAtMobile";

import { CaseStudyHeaderProps } from "./types";
import Container from "@/components/ui/Container";
import RichEditorClient from "@/components/bloks/RichText/RichEditorClient";

const CaseStudyHeader: React.FC<CaseStudyHeaderProps> = ({
  pageTitle = "",
  fullSlug = "",
  caseStudyHeaderBlok,
}) => {
  const bgList = caseStudyHeaderBlok?.background?.split(",");
  const backgroundImage = bgList?.[0] ?? "";
  const backgroundImageOfMobile = bgList?.[1] ?? "";

  const backgroundStyle = {
    "--mobile-bg": backgroundImageOfMobile
      ? `linear-gradient(${hexToRgba(caseStudyHeaderBlok.overlay, 0.9)}, ${hexToRgba(caseStudyHeaderBlok.overlay, 0.9)}), url(${backgroundImageOfMobile}) lightgray 50% 0% / 140% auto no-repeat`
      : "lightgray",
    "--desktop-bg": backgroundImage
      ? `linear-gradient(${hexToRgba(caseStudyHeaderBlok.overlay, 0.9)}, ${hexToRgba(caseStudyHeaderBlok.overlay, 0.9)}), url(${backgroundImage}) lightgray 100% 0% / 210% auto no-repeat`
      : "lightgray",
  } as React.CSSProperties;

  const studentInfoMarginRight =
    "mr-none md:mr-none lg:mr-[58px] xl:mr-[58px] 2xl:mr-[58px]";

  const rightContentWidth =
    "md:w-[274px] lg:w-[374px] xl:w-[374px] 2xl:w-[374px]";

  const rightContentPadding =
    "md:px-[18px] lg:px-[25px] xl:px-[25px] 2xl:px-[25px]";

  return (
    <>
      <div className="hidden md:block">
        <div
          className={cn(
            "relative h-[718px] w-full md:h-[600px] lg:h-[600px]",
            "[background:var(--mobile-bg)] md:[background:var(--desktop-bg)] lg:[background:var(--desktop-bg)]",
          )}
          style={backgroundStyle}
        >
          <Container
            size="caseStudy"
            className="flex h-full items-stretch justify-between !py-0"
          >
            <div
              className={cn(
                "flex h-full flex-col items-center justify-center text-center text-white",
                "max-w-[336px] md:max-w-[368px] lg:max-w-[368px] xl:max-w-[543px] 2xl:max-w-[543px]",
              )}
            >
              <Text
                tag="h1"
                style="sh2"
                mdStyle="sh1"
                className="text-left text-white"
              >
                {caseStudyHeaderBlok.heading}
              </Text>
              <Text
                tag="p"
                style="ph2"
                className="mt-[18px] text-left text-white"
              >
                {caseStudyHeaderBlok.bodyContent}
              </Text>
            </div>
            <div
              className={cn(
                studentInfoMarginRight,
                "flex h-full flex-col items-center justify-end",
              )}
            >
              <div
                className={cn(
                  rightContentPadding,
                  "flex flex-col items-center justify-center rounded-t-[4px] bg-[#fff]",
                )}
              >
                <Text tag="p" style="hw1" className="my-[18px]">
                  {caseStudyHeaderBlok.studentName}
                </Text>
                {!!caseStudyHeaderBlok?.studentImage?.filename && (
                  <Image
                    width={374}
                    height={350}
                    className={cn(rightContentWidth, "")}
                    src={caseStudyHeaderBlok.studentImage.filename}
                    alt={generateImageAltFromFilename(
                      caseStudyHeaderBlok.studentImage.filename,
                    )}
                  />
                )}

                <AcceptToAtTablet caseStudyHeaderBlok={caseStudyHeaderBlok} />
              </div>
            </div>
          </Container>

          <Container
            size="caseStudy"
            className="absolute bottom-[20px] left-1/2 -translate-x-1/2 !py-0"
          >
            <ShareButton
              title={pageTitle}
              fullSlug={fullSlug}
              iconColor="stroke-white"
            />
          </Container>
        </div>

        <div className="size-full bg-transparent">
          <Container
            size="caseStudy"
            className="flex h-full items-stretch justify-between !py-0"
          >
            <div
              className={cn(
                "flex h-full flex-col items-start justify-center",
                "max-w-[322px] md:max-w-[317px] lg:max-w-[317px] xl:max-w-[483px] 2xl:max-w-[528px]",
                "md:py-[75px] lg:py-[75px] xl:py-[95px] 2xl:py-[95px]",
              )}
            >
              <Text tag="h5" style="h5" className="text-primary01-50">
                {caseStudyHeaderBlok.bioSubHeading}
              </Text>
              <Text tag="h3" style="sh3" className="mb-[30px] mt-[20px]">
                {caseStudyHeaderBlok.bioHeading}
              </Text>

              <div>
                {caseStudyHeaderBlok.bioBodyContent?.map(
                  (RichTextContent, index) => (
                    <RichEditorClient value={RichTextContent.content} key={index} />
                  ),
                )}
              </div>
            </div>

            <div
              className={cn(
                rightContentWidth,
                rightContentPadding,
                studentInfoMarginRight,
                "mb-[75px] box-content flex h-full flex-col items-start justify-start gap-[25px] rounded-b-[4px] bg-white pb-[20px] md:mr-[0] md:mr-none md:pt-[13px] lg:pt-[30px] xl:pt-[30px] 2xl:pt-[30px]",
              )}
            >
              <StudentInfoAtDesktop caseStudyHeaderBlok={caseStudyHeaderBlok} />
            </div>
          </Container>
        </div>
      </div>
      <div className="block md:hidden">
        <div
          className={cn(
            "relative flex h-[718px] w-full",
            "[background:var(--mobile-bg)]",
          )}
          style={backgroundStyle}
        >
          <Container
            size="caseStudy"
            className="flex flex-col items-center justify-center"
          >
            <Text
              tag="h1"
              style="sh2"
              mdStyle="sh1"
              className="text-left text-white"
            >
              {caseStudyHeaderBlok.heading}
            </Text>
            <Text
              tag="p"
              style="ph2"
              className="mt-[25px] text-left text-white"
            >
              {caseStudyHeaderBlok.bodyContent}
            </Text>
          </Container>

          <div className="absolute bottom-[20px] left-[25px] md:bottom-[20px] md:left-[30px] lg:bottom-[20px] lg:left-[75px] xl:bottom-[20px] xl:left-[75px] 2xl:bottom-[20px] 2xl:left-[166px]">
            <ShareButton
              title={pageTitle}
              fullSlug={fullSlug}
              iconColor="stroke-white"
            />
          </div>
        </div>
        <Container
          size="caseStudy"
          className="flex flex-col items-center justify-start bg-white"
        >
          <Text tag="p" style="hw1" className="my-[16px]">
            {caseStudyHeaderBlok.studentName}
          </Text>
          {!!caseStudyHeaderBlok?.studentImage?.filename && (
            <Image
              width={330}
              height={308}
              className="w-full"
              src={caseStudyHeaderBlok.studentImage.filename}
              alt={generateImageAltFromFilename(
                caseStudyHeaderBlok.studentImage.filename,
              )}
            />
          )}

          <div className="flex w-full flex-col items-start justify-start gap-[25px] py-[30px]">
            <StudentInfoAtMobile caseStudyHeaderBlok={caseStudyHeaderBlok} />
          </div>
        </Container>
        <Container
          size="caseStudy"
          className="flex flex-col items-center justify-start bg-grays-G6 !py-[40px]"
        >
          <Text
            tag="h5"
            style="h5"
            className="w-full text-left text-primary01-50"
          >
            {caseStudyHeaderBlok.bioSubHeading}
          </Text>
          <Text tag="h3" style="sh3" className="mb-[30px] mt-[20px]">
            {caseStudyHeaderBlok.bioHeading}
          </Text>
          <div>
            {caseStudyHeaderBlok.bioBodyContent?.map(
              (RichTextContent, index) => (
                <RichEditorClient value={RichTextContent.content} key={index}/>
              ),
            )}
          </div>
        </Container>
      </div>
    </>
  );
};

export default CaseStudyHeader;
