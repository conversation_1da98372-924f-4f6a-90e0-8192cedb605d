import ssa<PERSON><PERSON>mon from "@crimson-education/algorithms-school-selection-common";
import React, { useEffect } from "react";

import { AddSubjectIcon, RemoveSubjectIcon } from "@/components/icons/Subjects";
import { cn } from "@/common/utils";

import { DeviceType } from "@/common/types";
import Text from "@/components/ui/Text";
import CountryField from "@/components/ui/FormInputFields/CountryField";
import PhoneNumberField from "@/components/ui/FormInputFields/PhoneNumberField";
import TextField from "@/components/ui/FormInputFields/TextField";
import SelectField from "@/components/ui/FormInputFields/SelectField";
import CheckboxField from "@/components/ui/FormInputFields/CheckboxField";
import RadioField from "@/components/ui/FormInputFields/RadioField";
import RectRadioField from "@/components/ui/FormInputFields/RectRadioField";

import { ICurriculumType, ISubjectType } from "./types";

import { useFormikContext, getIn } from "formik";

import {
  IBALevelGpaBlokProps,
  IBALevelGpaFormValues,
  A_LEVEL_GRADES,
  IB_GRADES,
} from "./types";
import { MAX_COUNT_OF_IB_SUBJECTS } from "./constants";

interface FormLayoutProps {
  step: number;
  blok: IBALevelGpaBlokProps;
  deviceType: DeviceType;
}

const FormLayout: React.FC<FormLayoutProps> = ({ step, blok, deviceType }) => {
  const { values, setFieldValue, errors, touched, setFieldError } =
    useFormikContext<IBALevelGpaFormValues>();

  const { IBSubjects, ALevelsSubjects } = ssaCommon;

  useEffect(() => {
    const newSubjectType =
      values.activeCurriculum === ICurriculumType.aLevel
        ? ISubjectType.A2
        : ISubjectType.HL;

    const needsUpdate = values.aLevelIBDetails.some(
      (detail) => detail.subjectType !== newSubjectType,
    );

    if (needsUpdate) {
      let updatedDetails = values.aLevelIBDetails.map(() => ({
        subjectType: newSubjectType,
        subject: "",
        grade: "",
      }));
      if (
        values.activeCurriculum === ICurriculumType.IB &&
        updatedDetails.length > MAX_COUNT_OF_IB_SUBJECTS
      ) {
        updatedDetails = updatedDetails.slice(0, MAX_COUNT_OF_IB_SUBJECTS);
      }
      void setFieldValue("aLevelIBDetails", updatedDetails);
    }
  }, [values.activeCurriculum, setFieldValue]); // eslint-disable-line react-hooks/exhaustive-deps

  const addSubject = async () => {
    const newSubject = {
      subject: "",
      subjectType:
        values.activeCurriculum === ICurriculumType.aLevel
          ? ISubjectType.A2
          : ISubjectType.HL,
      grade: "",
    };
    await setFieldValue("aLevelIBDetails", [
      ...values.aLevelIBDetails,
      newSubject,
    ]);
  };

  const removeSubject = async (index: number) => {
    const updatedSubjects = values.aLevelIBDetails.filter(
      (_, i) => i !== index,
    );
    await setFieldValue("aLevelIBDetails", updatedSubjects);
  };

  const getSubjectOptions = () => {
    const subjects =
      values.activeCurriculum === ICurriculumType.aLevel
        ? ALevelsSubjects
        : IBSubjects;
    return subjects.map((subject) => ({
      value: subject,
      label: subject,
    }));
  };

  const getGradeOptions = () => {
    return values.activeCurriculum === ICurriculumType.aLevel
      ? A_LEVEL_GRADES
      : IB_GRADES;
  };

  useEffect(() => {
    if (step === 2) {
      const subjects = values.aLevelIBDetails;
      const subjectNames = subjects
        .map((s) => s.subject)
        .filter((s) => s.trim() !== "");

      subjects.forEach((_, index) => {
        const currentError = getIn(errors, `aLevelIBDetails.${index}.subject`);
        if (currentError === "Duplicate subjects are not allowed") {
          setFieldError(`aLevelIBDetails.${index}.subject`, undefined);
        }
      });

      const duplicates = new Set<string>();
      const duplicateIndexes = new Set<number>();

      subjectNames.forEach((subject, index) => {
        const firstIndex = subjectNames.indexOf(subject);
        if (firstIndex !== index) {
          duplicates.add(subject);
          duplicateIndexes.add(index);
          duplicateIndexes.add(firstIndex);
        }
      });

      duplicateIndexes.forEach((index) => {
        setFieldError(
          `aLevelIBDetails.${index}.subject`,
          "Duplicate subjects are not allowed",
        );
      });
    }
  }, [values.aLevelIBDetails, step, setFieldError, errors]);

  if (step === 1) {
    return (
      <>
        <Text
          tag="p"
          style="b1"
          className="mb-6 w-full text-center text-primary01-100 md:mb-5xl"
        >
          {blok.subheading}
        </Text>

        <div className="flex flex-col justify-center gap-4 sm:flex-row sm:gap-4">
          <RectRadioField
            name="activeCurriculum"
            value="aLevel"
            label={blok.aLevelLabel ?? "A-Level"}
            theme="light"
            className="flex-1"
          />
          <RectRadioField
            name="activeCurriculum"
            value="IB"
            label={blok.ibLabel ?? "IB"}
            theme="light"
            className="flex-1"
          />
        </div>
      </>
    );
  }

  if (step === 2) {
    const isALevel = values.activeCurriculum === ICurriculumType.aLevel;
    const subjectType1 = isALevel ? ISubjectType.A2 : ISubjectType.HL;
    const subjectType2 = isALevel ? ISubjectType.AS : ISubjectType.SL;
    const maxSubjects = isALevel ? 7 : 6;

    return (
      <>
        <div className="mb-6 text-center md:mb-5xl">
          <Text tag="p" style="b1" className="mb-2 text-primary01-75">
            {isALevel ? blok.aLevelHeading : blok.IBHeading}
          </Text>
          <Text tag="p" style="b1" className="text-[#000]">
            {isALevel ? blok.aLevelSubheading : blok.IBSubheading}
          </Text>
        </div>

        <>
          {values.aLevelIBDetails.map((_, index) => (
            <div
              key={index}
              className={cn(
                "bg-gray-50 relative rounded-lg",
                index === 0 ? "" : "mt-[1.5rem] sm:mt-[2.5rem]",
              )}
            >
              <div className="hidden sm:flex sm:items-stretch sm:justify-between sm:gap-6">
                <div className="flex-[0.54]">
                  <SelectField
                    nameOfField={`aLevelIBDetails.${index}.subject`}
                    label={`${blok.subjectLabel} ${index + 1}`}
                    placeholder={blok.subjectPlaceholder}
                    options={getSubjectOptions()}
                    theme="light"
                    error={getIn(errors, `aLevelIBDetails.${index}.subject`)}
                    touched={getIn(touched, `aLevelIBDetails.${index}.subject`)}
                  />
                </div>

                <div className="flex shrink-0 flex-col items-end justify-end">
                  <div
                    className={`flex h-[65px] items-center sm:gap-6 ${
                      (getIn(errors, `aLevelIBDetails.${index}.subject`) &&
                        getIn(touched, `aLevelIBDetails.${index}.subject`)) ||
                      (getIn(errors, `aLevelIBDetails.${index}.grade`) &&
                        getIn(touched, `aLevelIBDetails.${index}.grade`))
                        ? "mb-[31px]"
                        : ""
                    }`}
                  >
                    <RadioField
                      name={`aLevelIBDetails.${index}.subjectType`}
                      value={subjectType1}
                      label={subjectType1}
                      theme="light"
                    />
                    <RadioField
                      name={`aLevelIBDetails.${index}.subjectType`}
                      value={subjectType2}
                      label={subjectType2}
                      theme="light"
                    />
                  </div>
                </div>

                <div className="flex-[0.46]">
                  <SelectField
                    nameOfField={`aLevelIBDetails.${index}.grade`}
                    label={blok.gradeLabel}
                    placeholder={blok.gradePlaceholder}
                    options={getGradeOptions()}
                    theme="light"
                    error={getIn(errors, `aLevelIBDetails.${index}.grade`)}
                    touched={getIn(touched, `aLevelIBDetails.${index}.grade`)}
                  />
                </div>

                <div className="flex shrink-0 flex-col items-end justify-end">
                  <button
                    type="button"
                    onClick={() => removeSubject(index)}
                    disabled={values.aLevelIBDetails.length <= 1}
                    className={`flex h-[65px] items-center sm:gap-6 ${
                      (getIn(errors, `aLevelIBDetails.${index}.subject`) &&
                        getIn(touched, `aLevelIBDetails.${index}.subject`)) ||
                      (getIn(errors, `aLevelIBDetails.${index}.grade`) &&
                        getIn(touched, `aLevelIBDetails.${index}.grade`))
                        ? "mb-[31px]"
                        : ""
                    }`}
                  >
                    <RemoveSubjectIcon />
                  </button>
                </div>
              </div>

              <div className="relative rounded-[8px] bg-neutral01-0 px-[12px] py-[18px] sm:hidden">
                <button
                  type="button"
                  onClick={() => removeSubject(index)}
                  disabled={values.aLevelIBDetails.length <= 1}
                  className="absolute right-[12px] top-[18px]"
                >
                  <RemoveSubjectIcon />
                </button>
                <div className="mb-4">
                  <SelectField
                    nameOfField={`aLevelIBDetails.${index}.subject`}
                    label={`${blok.subjectLabel} ${index + 1}`}
                    placeholder={blok.subjectPlaceholder}
                    options={getSubjectOptions()}
                    theme="light"
                    error={getIn(errors, `aLevelIBDetails.${index}.subject`)}
                    touched={getIn(touched, `aLevelIBDetails.${index}.subject`)}
                  />
                </div>
                <div className="mb-4 flex items-center justify-center gap-[56px]">
                  <RadioField
                    name={`aLevelIBDetails.${index}.subjectType`}
                    value={subjectType1}
                    label={subjectType1}
                    theme="light"
                  />
                  <RadioField
                    name={`aLevelIBDetails.${index}.subjectType`}
                    value={subjectType2}
                    label={subjectType2}
                    theme="light"
                  />
                </div>
                <div>
                  <SelectField
                    nameOfField={`aLevelIBDetails.${index}.grade`}
                    label={blok.gradeLabel}
                    placeholder={blok.gradePlaceholder}
                    options={getGradeOptions()}
                    theme="light"
                    error={getIn(errors, `aLevelIBDetails.${index}.grade`)}
                    touched={getIn(touched, `aLevelIBDetails.${index}.grade`)}
                  />
                </div>
              </div>
            </div>
          ))}

          {values.aLevelIBDetails.length < maxSubjects && (
            <button
              type="button"
              onClick={addSubject}
              className="mt-[24px] flex w-fit items-center justify-start gap-2"
            >
              <AddSubjectIcon className="size-5" />
              <Text tag="p" style="b1" className="!text-[16px] text-black">
                {blok.addSubjectLabel}
              </Text>
            </button>
          )}
        </>
      </>
    );
  }

  return (
    <>
      <div className="mb-6 text-center md:mb-5xl">
        <Text tag="p" style="b1" className="text-primary01-75">
          {blok.formSubheading}
        </Text>
      </div>

      <div
        className={cn(
          "grid gap-x-5 gap-y-6 md:gap-y-10 lg:gap-y-10",
          deviceType === DeviceType.Desktop ? "grid-cols-2" : "grid-cols-1",
        )}
      >
        <TextField
          nameOfField="firstName"
          label={blok.firstNamePlaceholder}
          placeholder={blok.firstNamePlaceholder}
          theme="light"
          error={errors?.firstName}
          touched={touched?.firstName}
        />
        <TextField
          nameOfField="lastName"
          label={blok.lastNamePlaceholder}
          placeholder={blok.lastNamePlaceholder}
          theme="light"
          error={errors.lastName}
          touched={touched.lastName}
        />
        <TextField
          nameOfField="email"
          label={blok.emailPlaceholder}
          placeholder={blok.emailPlaceholder}
          theme="light"
          error={errors.email}
          touched={touched.email}
        />

        <CountryField
          label={blok.countryLabel}
          language="en"
          nameOfField="country"
          setFieldValue={setFieldValue as any}
          theme="light"
          placeholder={blok.countryLabel}
          error={errors.country}
          touched={touched.country}
        />

        <TextField
          nameOfField="schoolName"
          label={blok.schoolNamePlaceholder}
          placeholder={blok.schoolNamePlaceholder}
          theme="light"
          error={errors?.schoolName}
          touched={touched?.schoolName}
        />
        <TextField
          type="number"
          nameOfField="gradeYearLevel"
          label={blok.gradeYearLevelPlaceholder ?? "Grade"}
          placeholder={blok.gradeYearLevelPlaceholder}
          theme="light"
          error={errors?.gradeYearLevel}
          touched={touched?.gradeYearLevel}
        />
      </div>

      <div className="mt-[24px] md:mt-[40px] lg:mt-[40px]">
        <PhoneNumberField
          label={blok.phoneNumberLabel}
          nameOfField="phoneNumber"
          localNumberFieldName="phoneNumber"
          countryCodeFieldName="countryCode"
          localNumberPlaceholder={blok.phoneNumberLabel}
          countryCodePlaceholder="▾"
          theme="light"
          formId="ibAlevelGpaForm"
          language="en"
          autofillCountryCodeByIP={true}
          errors={{
            countryCode: {
              value: errors.countryCode?.value ?? "",
              label: errors.countryCode?.label,
            },
            localNumber: errors.phoneNumber ?? "",
          }}
          touched={{
            countryCode: {
              value: touched.countryCode?.value ?? false,
              label: touched.countryCode?.label ?? false,
            },
            localNumber: touched.phoneNumber ?? false,
          }}
          values={{
            countryCode: values.countryCode,
            localNumber: values.phoneNumber,
          }}
          enableCountryLinking
        />
      </div>

      <div className="mt-[24px] md:mt-[40px] lg:mt-[40px]">
        <CheckboxField
          nameOfField="privacyPolicy"
          label={blok.privacyPolicyLabel}
          theme="light"
          error={errors.privacyPolicy}
          touched={touched.privacyPolicy}
          value={values?.privacyPolicy}
          formId="IbALevelForm"
          renderMarkdown
        />
      </div>
      <div className="mt-[24px] md:mt-[32px] lg:mt-[32px]">
        <CheckboxField
          nameOfField="commsOptIn"
          label={blok.studyPathwaysLabel}
          theme="light"
          error={errors.commsOptIn}
          touched={touched.commsOptIn}
          value={values?.commsOptIn}
          formId="IbALevelForm"
        />
      </div>
    </>
  );
};

export default FormLayout;
