import Container from "@/components/ui/Container";
import { getShortCardList } from "@/common/profile/getShortCardList";
import HeadingSection from "@/components/ui/CardProfilePreview/HeadingSection";
import ShortProfileCard from "./ShortProfileCard";
import ShowMoreShortCard from "./ShowMoreShortCard";

interface ShortCardProps {
  locale: string;
  blok: {
    preHeading?: string;
    heading?: string;
    bodyContent?: string;
    profiles: string[];
  };
}

const ShortCardProfilePreviewSection = async ({
  blok,
  locale = "en",
}: ShortCardProps) => {

  const { preHeading = "", heading, bodyContent, profiles } = blok;

  const fullList = await getShortCardList(profiles, locale);
  if (!fullList.length) return null;
  const initialProfiles = fullList.slice(0, 6);
  const restProfiles = fullList.slice(6);

  return (
    <Container>
      <HeadingSection
        preHeading={preHeading}
        heading={heading}
        bodyContent={bodyContent}
      />

      <div className="mx-auto mt-16 flex flex-wrap gap-x-[2.375rem] gap-y-16">
        {initialProfiles.map((profile) => (
          <div
            key={profile.uuid}
            className="flex w-full sm:w-[calc((100%-38px)/2)] lg:w-[calc((100%-38px*2)/3)]"
          >
            <ShortProfileCard profileInfo={profile} />
          </div>
        ))}
      </div>
      {restProfiles.length > 0 && (
        <ShowMoreShortCard profileList={restProfiles} />
      )}
    </Container>
  );
};

export default ShortCardProfilePreviewSection;
