import Image from "next/image";
import ProfileBgc from "@/images/Profile-background.webp";
import { cn } from "@/common/utils";
import LinkedIn from "@/components/icons/LinkedIn";
import LinkedInOutline from "@/components/icons/LinkedInOutLine";

interface Props {
  src: string;
  Name: string;
  className?: string;
  LinkedinProfileURL?: string | null;
}

const ProfilePic = ({ src, Name, LinkedinProfileURL, className }: Props) => {
  return (
    <div className={cn("relative aspect-[332/368] w-full shrink-0", className)}>
      <div className="absolute bottom-0 left-0 z-[1] h-[94%] w-full">
        <Image
          src={ProfileBgc}
          alt="Profile background"
          fill
          className="rounded object-cover object-top"
        />
      </div>

      <div className="absolute left-1/2 top-0 z-[2] size-full -translate-x-1/2">
        <Image
          src={src}
          alt={Name}
          fill
          className="rounded-t object-cover object-top"
        />
      </div>

      {LinkedinProfileURL && (
        <a
          href={LinkedinProfileURL}
          target="_blank"
          rel="noopener noreferrer"
          className="group absolute bottom-[1.875rem] right-[1.875rem] z-[4] size-12"
        >
          <div className="relative size-full">
            <LinkedInOutline className="absolute inset-0 size-full opacity-100 transition-opacity duration-300 group-hover:opacity-0" />
            <LinkedIn className="absolute inset-0 size-full text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          </div>
        </a>
      )}

      <div
        className="pointer-events-none absolute bottom-0 left-0 z-[3] h-[17%] w-full rounded-b-sm"
        style={{
          background: `linear-gradient(180deg, rgba(0, 0, 0, 0.00) 3.73%, rgba(0, 0, 0, 0.52) 41.7%, rgba(0, 0, 0, 0.90) 80.44%)`,
        }}
      />
    </div>
  );
};

export default ProfilePic;
