import { fetchStoryblokStories } from "../storyblok";
import { IProfileStory } from "@/common/types";

export async function getShortCardList(
  profileUuids: string[],
  locale: string,
  maxCount = 48,
): Promise<IProfileStory[]> {
  if (!profileUuids?.length) return [];

  const paths = [
    `${locale}/about-us/`,
    `${locale}/editors/`,
  ];

  let responses = [];
  try {
    responses = await Promise.all(
      paths.map((path) =>
        fetchStoryblokStories<IProfileStory>({
          draftMode: false,
          starts_with: path,
          filter_query: {
            component: { in: "profilePage" },
          },
          per_page: 100,
          sort_by: "published_at",
        }),
      ),
    );
  } catch (error) {
    console.error("Storyblok error, fallback to empty:", error);
    responses = paths.map(() => ({ data: { stories: [] } }));
  }

  const stories = responses.flatMap((res) => res.data?.stories ?? []);

  const matched = profileUuids
    .map((uuid) => stories.find((s) => s.uuid === uuid))
    .filter((s): s is IProfileStory => Boolean(s))
    .slice(0, maxCount);

  return matched;
}
