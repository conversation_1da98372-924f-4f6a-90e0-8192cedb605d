
import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { siteUrl } from "@/common/constants";
import {  StoryData } from "@/common/types";
import {
  fetchSiteShellWithFallback,
  fetchStoryBySlug,
} from "@/common/storyblok";
import { PageContextProvider } from "@/components/context/PageContext";
import SiteShell from "@/components/bloks/layout/SiteShell";
import { StoryblokComponent } from "@storyblok/react/rsc";
import NotFoundPage from "@/components/pages/NotFoundPage";
import { generateStoryMetadata } from "@/common/metaData/generateStoryMetadata";

type Props = {
  params: Promise<{ slug: string[] }>;
};

const draftMode = process.env.NEXT_PUBLIC_BUILD_ENV === "prod" ? false : true;
const defaultLocale = "us";

// https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamic
export const dynamic = "auto";

function generate404Metadata(locale: string, slug: string): Metadata {
  const title = `Page Is Not Found - Crimson Education ${locale}`;
  const description = "Page Is Not Found";
  const originalPageUrl = `${siteUrl}/${slug}`;

  return {
    metadataBase: new URL(siteUrl),
    title: title,
    description: description,
    robots: {
      index: false,
      follow: true,
    },
    alternates: {
      canonical: originalPageUrl,
    },
    icons: {
      icon: '/favicon-404.ico',
      shortcut: '/favicon-404.ico',
      apple: '/apple-touch-icon-404.png',
    },
    openGraph: {
      title: title,
      description: description,
    },
    twitter: {
      title: title,
      description: description,
    },
  };
}

async function getFormFieldData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/form/data-source/course-interest-list`,
  );
  const data = await res.json();
  return data;
}

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  const joinedSlug = slug.join("/");
  const locale = slug[0] ?? defaultLocale;

  try {
    return await generateStoryMetadata(joinedSlug);
  } catch (error) {
    return generate404Metadata(locale, joinedSlug);
  }
}



export default async function Page({ params }: Props) {
  const { slug } = await params;
  const joinedSlug = slug.join("/");
  const locale = slug[0] ?? defaultLocale;

  if (
    joinedSlug?.includes("events/") &&
    joinedSlug?.split("events/")[1]?.includes("/")
  ) {
    return (
      <div className="container mx-auto w-full px-4 py-24 text-center text-3xl">
        <p className="text-red-500">
          {`EventPageV2 only renders in "{locale}/events" folder, and not in any subfolders inside "events".  Please move this story to the appropriate folder.`}
        </p>
      </div>
    );
  }

  try {
    const { data } = await fetchStoryBySlug(joinedSlug, draftMode);
    const { story } = data as StoryData;

    if (!story) {
      throw new Error("Story not found");
    }

    const { content } = story;
    let coursesInterestedIn = null;
    const siteShellContent = await fetchSiteShellWithFallback(
      locale,
      draftMode,
      "en",
    );

    if (content?.component === "WebinarEventPageV2") {
      const formFieldData = getFormFieldData();
      [coursesInterestedIn] = await Promise.all([formFieldData]);
    }

    const pageContext = {
      locale,
      coursesInterestedIn,
    };

    return (
      <PageContextProvider values={pageContext}>
        <SiteShell
          locale={locale}
          pageType={content?.component}
          siteShellContent={siteShellContent}
        >
          <div className="mx-auto">
            <StoryblokComponent
              blok={content}
              story={story}
              siteShellContent={siteShellContent}
              locale={locale}
              slug={slug}
              publishedAt={story.published_at}
              fullSlug={story.full_slug}
            />
          </div>
        </SiteShell>
      </PageContextProvider>
    );
  } catch (error) {
    const siteShellContent = await fetchSiteShellWithFallback(locale, draftMode, "en");

    return (
      <PageContextProvider values={{ locale, coursesInterestedIn: null }}>
        <SiteShell
          locale={locale}
          pageType="NotFoundPage"
          siteShellContent={siteShellContent}
        >
          <NotFoundPage locale={locale} />
        </SiteShell>
      </PageContextProvider>
    );
  }
}
